import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, Send, Spark<PERSON>, Brain, ArrowLeft, Stethoscope, X, History, Search, Trash2, Clock, Plus, MessageSquare, ChevronLeft, ChevronRight, MessageCircle, ImageIcon, Loader2, TriangleAlert } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';

import { useAuth } from '@/hooks/useAuth';
import { useUserData } from '@/hooks/useUserData';
import { useDrWillChat } from '@/hooks/useDrWillChat';
import { useDrWillHistory } from '@/hooks/useDrWillHistory';
import { DrWillHistoryPanel } from '@/components/DrWillSidebar';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useIsMobile } from '@/hooks/use-mobile';
import { formatDrWillMessage, formatThinkingContent } from '@/utils/messageFormatter';
import { drWillLogger } from '@/utils/logger';
import { authDebugLogger } from '@/utils/authDebugLogger';
import { ThinkingModeComponent } from '@/components/ThinkingModeComponent';
import { DrWillDiagnostics } from '@/components/DrWillDiagnostics';
import MermaidModal from '@/components/MermaidModal';
import { useCurrentQuestion } from '@/contexts/CurrentQuestionContext';
import { SessionInactiveDialog } from '@/components/SessionInactiveDialog';
import { supabase } from '@/integrations/supabase/client';
import { MedicationResourcesDialog } from '@/components/dr-will/MedicationResourcesDialog';
import { MessageImageViewer } from '@/components/MessageImageViewer';
import { useToast } from '@/hooks/use-toast';
// import { useRenderLogger, useDependencyTracker } from '@/utils/renderLogger';

// 🎯 CACHE GLOBAL PARA FORMATAÇÃO (FORA DO COMPONENTE)
const formatCache = new Map<string, string>();

// 🎯 COMPONENTE ISOLADO PARA MENSAGENS - EVITA RE-RENDERS DESNECESSÁRIOS
const IsolatedMessageContent = React.memo(({
  content,
  isStreaming,
  messageId,
  isUser,
  isLoading,
  onResourcesClick,
  onResourcesDataDetected
}: {
  content: string;
  isStreaming: boolean;
  messageId: string;
  isUser: boolean;
  isLoading: boolean;
  onResourcesClick?: () => void;
  onResourcesDataDetected?: (data: any) => void;
}) => {
  // 🎯 PROCESSAR APENAS RAG (sugestões removidas para design limpo)
  const { content: cleanContent, ragData } = useMemo(() => {
    if (isUser || isStreaming) {
      return { content, ragData: null };
    }

    // 🔗 DETECTAR DADOS RAG (primeira ocorrência apenas)
    const ragMatch = content.match(/\*\*RAG_RESOURCES_BUTTON:(.*?)\*\*/) ||
                    content.match(/RAG_RESOURCES_BUTTON:(.*?)(?=\n|$)/);

    let ragData = null;
    let contentWithoutRag = content;

    if (ragMatch) {
      try {
        ragData = JSON.parse(ragMatch[1]);
        // Remover TODAS as ocorrências de RAG_RESOURCES_BUTTON
        contentWithoutRag = content
          .replace(/\*\*RAG_RESOURCES_BUTTON:.*?\*\*/g, '')
          .replace(/RAG_RESOURCES_BUTTON:.*?(?=\n|$)/g, '')
          .replace(/\n{3,}/g, '\n\n')
          .trim();
      } catch (error) {
        // Silently fail RAG parsing
      }
    }

    // 🚫 SUGESTÕES REMOVIDAS - apenas limpar o conteúdo de sugestões se existirem
    const contentWithoutSuggestions = contentWithoutRag.replace(/\$~~~SUGGESTIONS\$[\s\S]*?(?=\n\n|\n$|$)/g, '').trim();

    return {
      content: contentWithoutSuggestions,
      ragData
    };
  }, [content, isUser, isStreaming]);

  // 🔗 NOTIFICAR QUANDO DADOS RAG SÃO DETECTADOS (baseado no MessageBubble)
  useEffect(() => {
    if (ragData && onResourcesDataDetected) {
      const dialogData = {
        directMentions: ragData.directMentions || [],
        suggestions: ragData.suggestions || [],
        conductMentions: ragData.conductMentions || [],
        conductSuggestions: ragData.conductSuggestions || [],
        title: `Recursos encontrados (${ragData.count})`
      };
      onResourcesDataDetected(dialogData);
    }
  }, [ragData, onResourcesDataDetected]);

  // 🎯 FORMATAÇÃO COM CACHE INTELIGENTE
  const formattedContent = useMemo(() => {
    // Criar chave única para cache
    const cacheKey = `${messageId}_${cleanContent.length}_${isStreaming}`;

    // Verificar cache primeiro
    if (formatCache.has(cacheKey)) {
      // Cache hit - usar conteúdo existente
      return formatCache.get(cacheKey)!;
    }

    // 🔧 LIMPAR DIVISORES ANTES DA FORMATAÇÃO
    const contentWithoutDividers = cleanContent
      .replace(/^---+$/gm, '') // Remover linhas que são apenas divisores
      .replace(/^\*\*\*+$/gm, '') // Remover linhas que são apenas asteriscos
      .replace(/^___+$/gm, '') // Remover linhas que são apenas underscores
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Remover quebras de linha excessivas
      .trim();

    // Formatar conteúdo limpo (sem sugestões e sem divisores)
    const formatted = formatDrWillMessage(contentWithoutDividers, isStreaming || false);
    formatCache.set(cacheKey, formatted);

    return formatted;
  }, [cleanContent, messageId, isStreaming]);

  return (
    <>
      {/* Conteúdo da mensagem */}
      <div
        dangerouslySetInnerHTML={{
          __html: formattedContent
        }}
        className="leading-relaxed text-sm"
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        }}
      />

      {/* RAG Elegante - Última linha centralizada */}
      {(() => {
        const shouldShowRAG = !isUser && !isStreaming && ragData && ragData.count > 0;

        return shouldShowRAG && (
          <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
            <div className="text-center">
              <button
                onClick={onResourcesClick}
                className="inline-flex items-center gap-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 text-sm font-medium hover:bg-green-50 dark:hover:bg-green-900/20 px-3 py-2 rounded-lg transition-all duration-200"
              >
                <span className="text-base">📚</span>
                <span>Acessar recursos citados na plataforma ({ragData.count})</span>
              </button>
            </div>
          </div>
        );
      })()}
    </>
  );
}, (prevProps, nextProps) => {
  // 🎯 COMPARAÇÃO ULTRA-ESPECÍFICA PARA EVITAR RE-RENDERS (sem sugestões)
  const isEqual = (
    prevProps.content === nextProps.content &&
    prevProps.isStreaming === nextProps.isStreaming &&
    prevProps.messageId === nextProps.messageId &&
    prevProps.isUser === nextProps.isUser &&
    prevProps.isLoading === nextProps.isLoading &&
    prevProps.onResourcesClick === nextProps.onResourcesClick &&
    prevProps.onResourcesDataDetected === nextProps.onResourcesDataDetected
  );

  return isEqual;
});

// 🎯 REMOVIDO: ThinkingModeComponent agora é compartilhado

const DrWill = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useUserData();
  const isMobile = useIsMobile();

  // 🎯 ESTADO LOCAL para garantir sincronização (DEVE VIR ANTES DOS OUTROS HOOKS)
  const [activeThreadId, setActiveThreadId] = useState<string | null>(null);

  // 🎯 REFATORAÇÃO ESPECÍFICA: Estado para forçar nova thread (DEVE VIR ANTES DO useDrWillChat)
  const [forceNewThread, setForceNewThread] = useState(false);

  // 🎯 CORREÇÃO: Usar hooks separados para evitar conflito de estado
  const {
    currentThreadId,
    threads,
    loadMessages: loadHistoryMessages,
    deleteThread,
    deleteAllThreads,
    clearCurrentConversation,
    loadThreads,
    saveMessage: saveToHistory,
    createNewThread,
    getConversationHistory
  } = useDrWillHistory();

  const {
    messages,
    isLoading,
    isStreaming,
    error,
    sendMessage,
    clearMessages,
    setMessages,
    cancelRequest
  } = useDrWillChat({
    currentThreadId: activeThreadId || currentThreadId, // 🎯 Priorizar activeThreadId
    saveToHistory,
    createNewThread,
    getConversationHistory
  });

  // Função para obter iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messageRefs = useRef<{[messageId: string]: HTMLElement | null}>({});
  const [inputMessage, setInputMessage] = useState('');
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [tableDialogOpen, setTableDialogOpen] = useState(false);
  const [currentTableData, setCurrentTableData] = useState<string[][]>([]);
  const [showDiagnostics, setShowDiagnostics] = useState(false);

  // State for Mermaid modal
  const [mermaidModalOpen, setMermaidModalOpen] = useState(false);
  const [currentMermaidCode, setCurrentMermaidCode] = useState('');

  // 🎯 Hook para contexto da questão (copiado do FloatingChatButton)
  const { currentQuestion, sessionId, sessionTitle, isInQuestionSession } = useCurrentQuestion();

  // 🎯 State para dialog de sessão inativa (copiado do FloatingChatButton)
  const [sessionDialogOpen, setSessionDialogOpen] = useState(false);
  const [selectedSessionTitle, setSelectedSessionTitle] = useState('');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);

  // 🎯 Estados para o dialog de recursos de medicamentos (do código antigo)
  const [medicationDialogOpen, setMedicationDialogOpen] = useState(false);
  const [medicationDialogData, setMedicationDialogData] = useState<{
    directMentions: any[];
    suggestions: any[];
    conductMentions: any[];
    conductSuggestions: any[];
    title: string;
  } | null>(null);

  // 🗑️ Estados para dialog de confirmação de deleção
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteDialogData, setDeleteDialogData] = useState<{
    type: 'single' | 'all';
    threadId?: string;
    threadTitle?: string;
  } | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // 🖼️ Estados para upload de imagens
  const [selectedImages, setSelectedImages] = useState<{ url: string; file: File }[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 🔍 MONITORAMENTO REMOVIDO TEMPORARIAMENTE PARA FOCAR NA SOLUÇÃO

  // 🔍 LOGS REMOVIDOS TEMPORARIAMENTE PARA FOCAR NA SOLUÇÃO



  // 🎯 SCROLL OTIMIZADO - Usar useRef para evitar re-renders
  const autoScrollEnabled = useRef(true);

  // 📊 LOADING STATES
  const [isHistoryLoading, setIsHistoryLoading] = useState(true);

  // Detectar quando usuário faz scroll manual - COM THROTTLE
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    // 🎯 THROTTLE PARA EVITAR MUITOS EVENTOS DE SCROLL
    let scrollTimeout: NodeJS.Timeout | null = null;

    const handleScroll = () => {
      // Cancelar timeout anterior se existir
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      // Executar após 100ms de inatividade
      scrollTimeout = setTimeout(() => {
        const { scrollTop, scrollHeight, clientHeight } = container;
        const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

        // Se usuário scrollou para longe do final, desabilitar auto-scroll
        if (!isNearBottom) {
          autoScrollEnabled.current = false;
        } else {
          // Se usuário voltou para perto do final, reabilitar auto-scroll
          autoScrollEnabled.current = true;
        }
      }, 100); // 100ms de throttle
    };

    container.addEventListener('scroll', handleScroll);
    return () => {
      container.removeEventListener('scroll', handleScroll);
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, []);

  // 📊 CONTROLE DE LOADING STATES
  useEffect(() => {
    // Controlar loading do histórico
    if (threads !== undefined) {
      setIsHistoryLoading(false);
    }
  }, [threads]);



  // Scroll inteligente com detecção de topo (COPIADO EXATO DO FLOATINGCHATBUTTON)
  const lastScrolledMessageId = useRef<string>('');
  const hasReachedTop = useRef<boolean>(false);
  const scrollAttempts = useRef<number>(0);

  useEffect(() => {
    if (messages.length === 0) return;

    const lastMessage = messages[messages.length - 1];

    // SEMPRE scroll para mensagem do usuário (aparecer no topo do chat)
    if (lastMessage.isUser) {
      lastScrolledMessageId.current = lastMessage.id;
      hasReachedTop.current = false; // Reset para próxima resposta
      scrollAttempts.current = 0; // Reset tentativas

      setTimeout(() => {
        const messageElement = messageRefs.current[lastMessage.id];
        const container = messagesContainerRef.current;
        if (messageElement && container) {
          const elementTop = messageElement.offsetTop;
          // Offset menor para Dr. Will (sem header flutuante)
          container.scrollTo({
            top: elementTop - 20, // 20px em vez de 80px
            behavior: 'smooth'
          });
        }
      }, 100);
    }
    // Scroll CONTÍNUO para Dr. Will durante streaming (apenas se autoScroll habilitado)
    else if (lastMessage.isStreaming && !lastMessage.isUser && !hasReachedTop.current && autoScrollEnabled.current) {
      setTimeout(() => {
        const messageElement = messageRefs.current[lastMessage.id];
        const container = messagesContainerRef.current;
        if (messageElement && container && !hasReachedTop.current) {
          const elementTop = messageElement.offsetTop;
          const targetScrollTop = elementTop - 20; // Mesmo offset de 20px
          const currentScrollTop = container.scrollTop;

          // Verificar se já chegou no topo (diferença menor que 30px)
          if (Math.abs(currentScrollTop - targetScrollTop) <= 30) {
            hasReachedTop.current = true;
            return;
          }

          // Continuar scrolling
          container.scrollTo({
            top: targetScrollTop,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
    // Quando Dr. Will terminar, garantir que parou
    else if (!lastMessage.isStreaming && !lastMessage.isUser && lastMessage.content) {
      hasReachedTop.current = true;
    }
  }, [messages]); // Removido autoScrollEnabled das dependências pois agora é useRef

  // Note: Scroll behavior is now handled by the messages useEffect above
  // This ensures we scroll when user message is added, regardless of loading/streaming states

  // No welcome message - always start fresh
  // User will start conversation when they send first message

  // 🔧 CORREÇÃO: Função local para limpar conversa como fallback
  const clearConversationSafe = useCallback(() => {
    if (clearCurrentConversation && typeof clearCurrentConversation === 'function') {
      clearCurrentConversation();
    }
    // Fallback: limpar estado local
    setActiveThreadId(null);
  }, [clearCurrentConversation]);

  const handleNewConversation = useCallback(() => {
    if (isLoading) return;

    clearMessages();
    clearConversationSafe();
    setInputMessage('');
    setShowHistory(false);
    setForceNewThread(true);

    // Scroll para o topo
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = 0;
    }
  }, [isLoading, clearMessages, clearConversationSafe]);

  // 🖼️ Função para upload de imagens
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) {
      return;
    }

    const newImages: { url: string; file: File }[] = [];

    for (let i = 0; i < Math.min(files.length, 3 - selectedImages.length); i++) {
      const file = files[i];
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file);
        newImages.push({ url, file });
      }
    }

    setSelectedImages(prev => [...prev, ...newImages]);

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 🖼️ Função para remover imagem
  const removeImage = (index: number) => {
    setSelectedImages(prev => {
      const newImages = [...prev];
      URL.revokeObjectURL(newImages[index].url);
      newImages.splice(index, 1);
      return newImages;
    });
  };

  const handleSendMessage = async (messageToSend?: string, imageUrl?: string | string[]) => {
    // Usar messageToSend se fornecido, senão usar inputMessage
    const actualMessage = messageToSend || inputMessage || '';

    const finalMessage = actualMessage.trim();
    if (!finalMessage && selectedImages.length === 0) {
      return;
    }
    if (isLoading) {
      return;
    }

    // Garantir que messageContent seja sempre uma string
    const messageContent = finalMessage;
    let imageUrls: string[] = [];

    // Upload das imagens selecionadas se houver
    if (selectedImages.length > 0) {
      try {
        setIsUploading(true);

        // 🔐 FALLBACK: Usar localStorage se user?.id não estiver disponível
        const userIdToUse = user?.id || localStorage.getItem('auth_user_id');

        if (!userIdToUse) {
          throw new Error('Usuário não autenticado. Faça login novamente.');
        }

        for (let i = 0; i < selectedImages.length; i++) {
          const image = selectedImages[i];
          const fileExt = image.file.name.split('.').pop();
          const fileName = `dr-will-uploads/${userIdToUse}/${crypto.randomUUID()}.${fileExt}`;



          const { error: uploadError } = await supabase.storage
            .from('chat-images') // Usar chat-images temporariamente
            .upload(fileName, image.file);

          if (uploadError) {
            throw uploadError;
          }

          const { data: { publicUrl } } = supabase.storage
            .from('chat-images') // Usar chat-images temporariamente
            .getPublicUrl(fileName);

          imageUrls.push(publicUrl);
        }

        // Limpar imagens selecionadas após upload
        selectedImages.forEach((img) => {
          URL.revokeObjectURL(img.url);
        });
        setSelectedImages([]);

      } catch (error: any) {
        toast({
          title: "Erro ao enviar imagem",
          description: error.message || "Erro desconhecido",
          variant: "destructive",
        });
        return;
      } finally {
        setIsUploading(false);
      }
    }

    // Usar imageUrl passado como parâmetro ou as URLs das imagens uploadadas
    const finalImageUrls = imageUrl || (imageUrls.length > 0 ? imageUrls : undefined);

    if (!messageToSend) {
      setInputMessage('');
    }

    // 🔍 DEBUG: Log do estado do user no DrWill


    authDebugLogger.drWillUserState({
      user: user ? { id: user.id, email: user.email } : null,
      hasUser: !!user,
      userId: user?.id,
      userType: typeof user,
      authState: {
        localStorage_userId: localStorage.getItem('auth_user_id'),
        localStorage_profile: localStorage.getItem('auth_profile') ? 'exists' : 'missing'
      }
    });

    try {
      const shouldForceNewThread = forceNewThread;

      if (shouldForceNewThread) {
        setForceNewThread(false);
        authDebugLogger.drWillSendMessageCall({ type: 'forceNewThread', userId: user?.id });
        await sendMessage(messageContent, user?.id, null, true, finalImageUrls);
      } else {
        const threadToUse = activeThreadId || currentThreadId;
        authDebugLogger.drWillSendMessageCall({ type: 'existingThread', userId: user?.id, threadToUse });
        await sendMessage(messageContent, user?.id, threadToUse, false, finalImageUrls);
      }
    } catch (error) {
      // Error handled silently
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 🚫 handleSuggestionClick removido - sugestões desabilitadas para design limpo

  // 🎯 Funções para recursos RAG (do código antigo)
  const handleResourcesDataDetected = useCallback((data: any) => {
    setMedicationDialogData(data);
  }, []);

  const handleResourcesClick = useCallback(() => {
    if (medicationDialogData) {
      setMedicationDialogOpen(true);
    }
  }, [medicationDialogData]);

  // 🗑️ Funções para dialog de confirmação de deleção
  const handleDeleteSingleThread = useCallback((threadId: string, threadTitle: string) => {
    setDeleteDialogData({
      type: 'single',
      threadId,
      threadTitle
    });
    setDeleteDialogOpen(true);
  }, []);

  const handleDeleteAllThreads = useCallback(() => {
    setDeleteDialogData({
      type: 'all'
    });
    setDeleteDialogOpen(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!deleteDialogData || isDeleting) return;

    setIsDeleting(true);
    try {
      if (deleteDialogData.type === 'single' && deleteDialogData.threadId) {
        await deleteThread(deleteDialogData.threadId);
      } else if (deleteDialogData.type === 'all') {
        await deleteAllThreads();
      }

      // Recarregar threads após deleção FORÇANDO RELOAD (sem cache)
      await loadThreads(true);

      // Fechar dialog
      setDeleteDialogOpen(false);
      setDeleteDialogData(null);
    } catch (error) {
      // Error handled silently
    } finally {
      setIsDeleting(false);
    }
  }, [deleteDialogData, deleteThread, deleteAllThreads, loadThreads, isDeleting, threads]);

  const cancelDelete = useCallback(() => {
    setDeleteDialogOpen(false);
    setDeleteDialogData(null);
  }, []);

  // 🎯 Função global para abrir o dialog (chamada pelo botão HTML - do código antigo)
  useEffect(() => {
    (window as any).openMedicationDialog = handleResourcesClick;

    return () => {
      delete (window as any).openMedicationDialog;
    };
  }, [handleResourcesClick]);

  // History state
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 10;

  // Função para detectar threads contextuais (copiada do FloatingChatButton)
  const isContextualThread = useCallback((title: string) => {
    return title.includes('📚') && (
      title.includes('Sessão Contextual') ||
      title.includes('(') && title.includes('q)')
    );
  }, []);

  // 🎯 Função para extrair nome da sessão do título da thread
  const extractSessionNameFromTitle = useCallback((threadTitle: string): string => {
    if (!threadTitle) return 'Sessão';

    // Remover emoji e limpar
    let sessionName = threadTitle.replace('📚 ', '');

    // Remover sufixos antigos
    sessionName = sessionName.replace(' - Sessão Contextual', '');

    // Remover informação de número de questões se presente
    sessionName = sessionName.replace(/\s*\(\d+q\)$/, '');

    return sessionName || 'Sessão';
  }, []);

  // 🎯 Função para buscar sessionId por título (copiada do FloatingChatButton)
  const findSessionIdByTitle = useCallback(async (sessionName: string): Promise<string | null> => {
    try {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('study_sessions')
        .select('id')
        .eq('user_id', user.id)
        .eq('title', sessionName)
        .single();

      if (error) {
        return null;
      }

      return data?.id || null;
    } catch (error) {
      return null;
    }
  }, [user?.id]);

  // 🎯 Função para mostrar dialog de sessão inativa (copiada do FloatingChatButton)
  const showSessionInactiveDialog = async (sessionName: string, threadId?: string) => {
    setSelectedSessionTitle(sessionName);

    // Primeiro tentar extrair sessionId do metadata da thread
    let sessionIdToUse: string | null = null;

    if (threadId) {
      const thread = threads?.find(t => t.id === threadId);

      if (thread && (thread as any).metadata?.sessionId) {
        sessionIdToUse = (thread as any).metadata.sessionId;
      }
    }

    // Se não encontrou no metadata, buscar por título
    if (!sessionIdToUse) {
      sessionIdToUse = await findSessionIdByTitle(sessionName);
    }

    setSelectedSessionId(sessionIdToUse);
    setSessionDialogOpen(true);
  };

  // History management functions - com lógica de sessão (copiada do FloatingChatButton)
  const handleSelectThread = async (threadId: string) => {
    try {
      if (isLoading) return;

      const thread = threads?.find(t => t.id === threadId);
      if (!thread) return;

      const isContextual = isContextualThread(thread.title || '');

      if (isContextual) {
        const sessionName = extractSessionNameFromTitle(thread.title || '');
        showSessionInactiveDialog(sessionName, thread.id);
        return;
      }

      const historyMessages = await loadHistoryMessages(threadId);
      setActiveThreadId(threadId);

      if (historyMessages && historyMessages.length > 0) {
        const chatMessages = historyMessages.map(msg => ({
          id: msg.id,
          content: msg.content,
          isUser: msg.isUser,
          timestamp: msg.timestamp,
          isStreaming: false
        }));

        setMessages(chatMessages);
      } else {
        clearMessages();
      }

      setIsHistoryOpen(false);
      setShowHistory(false);

    } catch (error) {
      drWillLogger.error('selecting thread', error);
    }
  };

  const handleNewThread = () => {
    clearConversationSafe();
    clearMessages();
    setIsHistoryOpen(false);
    setShowHistory(false);
    setForceNewThread(true);
  };

  const handleDeleteThread = async (threadId: string) => {
    try {
      await deleteThread(threadId);

      // If we deleted the current thread, start a new conversation
      if (threadId === activeThreadId || threadId === currentThreadId) {
        clearMessages();
        setActiveThreadId(null);
      }

      // 🔄 RECARREGAR LISTA DE THREADS APÓS DELEÇÃO (FORÇANDO RELOAD)
      await loadThreads(true);

    } catch (error) {
      drWillLogger.error('deleting thread', error);
    }
  };



  // Filter threads based on search query and filter type (copiado do FloatingChatButton)
  const filteredThreads = useMemo(() => {


    if (!threads) return [];

    // Mostrar apenas threads gerais (não contextuais)
    let filtered = threads.filter((thread) => {
      return !isContextualThread(thread.title || '');
    });

    // Apply search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(thread =>
        thread.title.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }



    return filtered;
  }, [threads, searchQuery, isContextualThread]);

  // Reset page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  // Format relative time
  const formatRelativeTime = (date: Date) => {
    try {
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: ptBR
      });
    } catch {
      return 'há alguns momentos';
    }
  };

  // Auto-create new thread on page load (sempre nova conversa)
  useEffect(() => {
    // Page loaded - ready for user interaction
  }, [user?.id]);

  // Global functions for table and mermaid
  useEffect(() => {
    // Função para abrir dialog de tabela
    (window as any).openTableDialog = (tableId: string) => {
      const dataElement = document.getElementById(`table-data-${tableId}`);
      if (dataElement) {
        try {
          const tableData = JSON.parse(dataElement.textContent || '[]');
          setCurrentTableData(tableData);
          setTableDialogOpen(true);
        } catch (error) {
          // Erro silencioso
        }
      }
    };

    // 🎯 ESCUTAR EVENTO CUSTOMIZADO PARA MERMAID
    const handleMermaidModalEvent = (event: any) => {
      if (event.detail && event.detail.mermaidCode) {
        setCurrentMermaidCode(event.detail.mermaidCode);
        setMermaidModalOpen(true);
      }
    };

    document.addEventListener('openMermaidModal', handleMermaidModalEvent);

    // Função global para expandir Mermaid (fallback)
    (window as any).expandMermaidDiagram = (id: string) => {
      // Tentar encontrar o código no script JSON
      const codeElement = document.getElementById(id + '-code');
      if (codeElement) {
        try {
          const mermaidCode = JSON.parse(codeElement.textContent || '');
          setCurrentMermaidCode(mermaidCode);
          setMermaidModalOpen(true);
          return;
        } catch (error) {
          // Erro silencioso
        }
      }

      // Fallback: procurar em todas as mensagens por código Mermaid
      const allMessages = document.querySelectorAll('[id^="mermaid-"]');
      if (allMessages.length > 0) {
        // Usar a última mensagem com Mermaid
        const lastMermaidId = Array.from(allMessages).pop()?.id;
        if (lastMermaidId) {
          const lastCodeElement = document.getElementById(lastMermaidId + '-code');
          if (lastCodeElement) {
            try {
              const mermaidCode = JSON.parse(lastCodeElement.textContent || '');
              setCurrentMermaidCode(mermaidCode);
              setMermaidModalOpen(true);
              return;
            } catch (error) {
              // Erro silencioso
            }
          }
        }
      }


    };

    // Inicializar Mermaid globalmente com detecção de modo escuro
    if (typeof (window as any).mermaid !== 'undefined') {
      const isDarkMode = document.documentElement.classList.contains('dark');

      (window as any).mermaid.initialize({
        startOnLoad: true,
        theme: isDarkMode ? 'dark' : 'default',
        themeVariables: isDarkMode ? {
          primaryColor: '#8b5cf6',
          primaryTextColor: '#f3f4f6',
          primaryBorderColor: '#6366f1',
          lineColor: '#9ca3af',
          background: '#1f2937',
          mainBkg: '#374151',
          secondBkg: '#4b5563',
          tertiaryColor: '#6b7280'
        } : {
          primaryColor: '#8b5cf6',
          primaryTextColor: '#1f2937',
          primaryBorderColor: '#6366f1',
          lineColor: '#6b7280'
        }
      });
    }

    return () => {
      delete (window as any).openTableDialog;
      delete (window as any).expandMermaidDiagram;
      document.removeEventListener('openMermaidModal', handleMermaidModalEvent);
    };
  }, []);

  // 🎯 FORMATAÇÃO ÚNICA - REMOVIDO getFormattedContent DUPLICADO
  // Usar diretamente formatDrWillMessage do messageFormatter.ts

  // 🎯 LIMPEZA DE CACHE QUANDO NECESSÁRIO
  useEffect(() => {
    // Limpar cache quando há muitas entradas (evitar memory leak)
    if (formatCache.size > 100) {
      // Limpeza de cache
      formatCache.clear();
    }
  }, [messages.length]);










  // 📱 CORREÇÃO MOBILE: Aplicar altura fixa apenas no Dr. Will
  useEffect(() => {
    if (isMobile) {
      // Salvar estilos originais
      const originalBodyHeight = document.body.style.height;
      const originalBodyMaxHeight = document.body.style.maxHeight;
      const originalHtmlHeight = document.documentElement.style.height;
      const originalHtmlMaxHeight = document.documentElement.style.maxHeight;
      const originalRootHeight = document.getElementById('root')?.style.height;
      const originalRootMaxHeight = document.getElementById('root')?.style.maxHeight;
      const originalRootOverflow = document.getElementById('root')?.style.overflow;

      // Aplicar correção específica para Dr. Will
      document.body.style.height = '100vh';
      document.body.style.maxHeight = '100vh';
      document.documentElement.style.height = '100vh';
      document.documentElement.style.maxHeight = '100vh';

      const rootElement = document.getElementById('root');
      if (rootElement) {
        rootElement.style.height = '100vh';
        rootElement.style.maxHeight = '100vh';
        rootElement.style.overflow = 'hidden';
      }

      // Cleanup: restaurar estilos originais quando sair da página
      return () => {
        document.body.style.height = originalBodyHeight;
        document.body.style.maxHeight = originalBodyMaxHeight;
        document.documentElement.style.height = originalHtmlHeight;
        document.documentElement.style.maxHeight = originalHtmlMaxHeight;

        if (rootElement) {
          rootElement.style.height = originalRootHeight || '';
          rootElement.style.maxHeight = originalRootMaxHeight || '';
          rootElement.style.overflow = originalRootOverflow || '';
        }
      };
    }
  }, [isMobile]);

  // 🎯 FUNÇÃO PRINCIPAL DO COMPONENTE
  return (
    <div className="h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex flex-col overflow-hidden">
      <Header />

      {/* History Panel */}
      <DrWillHistoryPanel
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
        threads={threads || []}
        currentThreadId={currentThreadId}
        onSelectThread={handleSelectThread}
        onNewThread={handleNewThread}
        onDeleteThread={handleDeleteThread}
        isLoading={isLoading}
      />

      <div
        data-drwill-container
        className={`flex-1 container max-w-4xl mx-auto ${isMobile ? 'px-2 py-2' : 'px-4 py-1'} flex flex-col overflow-hidden ${isMobile ? 'pb-20' : ''} ${!isMobile ? 'h-[calc(100vh-80px)]' : ''}`}>
        {/* Header - Todos na mesma linha no Mobile */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`${isMobile ? 'flex items-center justify-between gap-2 mb-2' : 'flex items-center gap-4 mb-2'} flex-shrink-0`}
        >
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/plataformadeestudos')}
              className="border-2 border-black dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {isMobile ? '' : 'Voltar'}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                if (!showHistory) {
                  // Carregar threads apenas quando abrir o histórico pela primeira vez
                  await loadThreads();
                }
                setShowHistory(!showHistory);
              }}
              className={`border-2 border-black dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 ${showHistory ? 'bg-purple-50 dark:bg-purple-900/30 border-purple-500 dark:border-purple-400' : ''}`}
            >
              <History className="h-4 w-4" />
              {!isMobile && <span className="ml-2">{showHistory ? 'Chat' : 'Histórico'}</span>}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleNewConversation}
              disabled={isLoading}
              className="border-2 border-black dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Plus className="h-4 w-4" />
              {!isMobile && <span className="ml-2">Nova</span>}
            </Button>

          </div>

          {isMobile ? (
            // Layout compacto para mobile - na mesma linha
            <div className="flex items-center gap-2">
              <div className="relative">
                <div className="p-1.5 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg border-2 border-black">
                  <Bot className="h-4 w-4 text-white" />
                  <Sparkles className="h-2 w-2 text-yellow-300 absolute -top-0.5 -right-0.5 animate-pulse" />
                </div>
              </div>
              <h1 className="text-sm font-bold text-gray-800 dark:text-gray-200">Dr. Will</h1>
            </div>
          ) : (
            // Layout desktop original
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl border-2 border-black">
                  <Bot className="h-8 w-8 text-white" />
                  <Sparkles className="h-4 w-4 text-yellow-300 absolute -top-1 -right-1 animate-pulse" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">Dr. Will</h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  MedEvo
                </p>
              </div>
            </div>
          )}
        </motion.div>

        {/* Chat Container */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className={`flex-1 bg-white dark:bg-gray-800 rounded-2xl border-2 border-black dark:border-gray-600 shadow-xl overflow-hidden relative flex flex-col ${!isMobile ? 'h-[calc(100vh-100px)]' : ''}`}
        >
          {/* Messages Area */}
          <div
            ref={messagesContainerRef}
            className={`flex-1 overflow-y-auto ${isMobile ? 'p-4' : 'p-6'} space-y-4 relative`}
          >
            <AnimatePresence mode="wait">
              {showHistory ? (
                // 📚 HISTÓRICO INTEGRADO
                <motion.div
                  key="history"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="h-full flex flex-col"
                >
                  {/* Header do Histórico com Botões de Deletar */}
                  <div className="p-3 border-b border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100">Conversas Anteriores</h4>

                      {/* Botões de Deletar */}
                      <div className="flex items-center gap-2">
                        <button
                          onClick={handleDeleteAllThreads}
                          disabled={isLoading || !threads || threads.length === 0}
                          className={`px-2 py-1 text-xs rounded transition-colors ${
                            isLoading || !threads || threads.length === 0
                              ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                              : 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50'
                          }`}
                          title="Deletar todas as conversas"
                        >
                          🗑️ Todas
                        </button>
                      </div>
                    </div>


                  </div>

                  {/* Lista de Threads - Copiado do FloatingChatButton */}
                  {isHistoryLoading ? (
                    <div className="space-y-3 p-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex items-center gap-3 animate-pulse">
                          <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : threads && threads.length > 0 ? (
                    (() => {
                      // Calcular paginação
                      const totalPages = Math.ceil(filteredThreads.length / ITEMS_PER_PAGE);
                      const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
                      const endIndex = startIndex + ITEMS_PER_PAGE;
                      const paginatedThreads = filteredThreads.slice(startIndex, endIndex);

                      return (
                        <>
                          {/* Lista de threads paginada */}
                          {paginatedThreads.map((thread) => (
                            <motion.div
                              key={thread.id}
                              whileHover={{ backgroundColor: '#f8fafc' }}
                              onClick={() => {
                                // Bloquear se estiver carregando
                                if (isLoading) {
                                  return;
                                }
                                handleSelectThread(thread.id);
                              }}
                              className={`p-3 border-b border-gray-100 transition-colors ${
                                isLoading
                                  ? 'opacity-50 cursor-not-allowed'
                                  : 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700'
                              }`}
                            >
                              <div className="flex items-start gap-3">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm ${
                                  isContextualThread(thread.title || '')
                                    ? 'bg-gradient-to-br from-emerald-500 to-teal-600'
                                    : 'bg-gradient-to-br from-blue-500 to-purple-600'
                                }`}>
                                  {isContextualThread(thread.title || '') ? '🎯' : '🧠'}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2">
                                    <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm truncate">
                                      {thread.title || 'Conversa com Dr. Will'}
                                    </h5>
                                    {isContextualThread(thread.title || '') && (
                                      <span className="px-2 py-0.5 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 text-xs rounded-full">
                                        Contextual
                                      </span>
                                    )}
                                  </div>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {(() => {
                                      try {
                                        if (thread.lastMessageAt) {
                                          const date = new Date(thread.lastMessageAt);
                                          if (!isNaN(date.getTime())) {
                                            return formatDistanceToNow(date, {
                                              addSuffix: true,
                                              locale: ptBR
                                            });
                                          }
                                        }
                                        return 'Recente';
                                      } catch {
                                        return 'Recente';
                                      }
                                    })()}
                                    {isContextualThread(thread.title || '') && (
                                      !isInQuestionSession ? (
                                        <span className="ml-2 text-orange-500">• Sem contexto ativo</span>
                                      ) : (
                                        (() => {
                                          // Usar sessionId do metadata da thread para comparação precisa
                                          const threadSessionId = (thread as any).metadata?.sessionId;
                                          const currentSessionId = sessionId;

                                          return threadSessionId && currentSessionId && threadSessionId !== currentSessionId ? (
                                            <span className="ml-2 text-red-500">• Sessão diferente</span>
                                          ) : threadSessionId && currentSessionId && threadSessionId === currentSessionId ? (
                                            <span className="ml-2 text-green-500">• Sessão atual</span>
                                          ) : (
                                            <span className="ml-2 text-gray-500">• Sessão</span>
                                          );
                                        })()
                                      )
                                    )}
                                  </p>
                                </div>

                                {/* Botão Deletar Individual */}
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation(); // Evitar trigger do onClick da thread
                                    handleDeleteSingleThread(thread.id, thread.title || 'Conversa com Dr. Will');
                                  }}
                                  disabled={isLoading}
                                  className={`p-1 rounded transition-colors ${
                                    isLoading
                                      ? 'text-gray-300 cursor-not-allowed'
                                      : 'text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30'
                                  }`}
                                  title="Deletar conversa"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </button>
                              </div>
                            </motion.div>
                          ))}

                          {/* Controles de Paginação - Copiado do FloatingChatButton */}
                          {totalPages > 1 && (
                            <div className="p-3 border-t border-gray-100 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
                              <div className="flex items-center justify-between">
                                <div className="text-xs text-gray-500">
                                  Página {currentPage} de {totalPages} • {filteredThreads.length} conversas
                                </div>
                                <div className="flex items-center gap-1">
                                  <button
                                    onClick={() => setCurrentPage(currentPage - 1)}
                                    disabled={currentPage === 1 || isLoading}
                                    className={`px-2 py-1 text-xs rounded transition-colors ${
                                      currentPage === 1 || isLoading
                                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                        : 'bg-white dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-500 border border-gray-200 dark:border-gray-500'
                                    }`}
                                  >
                                    ←
                                  </button>
                                  <span className="px-2 py-1 text-xs text-gray-600">
                                    {currentPage}
                                  </span>
                                  <button
                                    onClick={() => setCurrentPage(currentPage + 1)}
                                    disabled={currentPage === totalPages || isLoading}
                                    className={`px-2 py-1 text-xs rounded transition-colors ${
                                      currentPage === totalPages || isLoading
                                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                        : 'bg-white dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-500 border border-gray-200 dark:border-gray-500'
                                    }`}
                                  >
                                    →
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Botão Voltar à Thread Atual - Copiado do FloatingChatButton */}
                          {currentThreadId && (
                            <div className="p-3 border-t border-gray-100 dark:border-gray-600 bg-blue-50 dark:bg-blue-900/30">
                              <button
                                onClick={() => {
                                  if (!isLoading) {
                                    setShowHistory(false);
                                  }
                                }}
                                disabled={isLoading}
                                className={`w-full px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                                  isLoading
                                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                    : 'bg-blue-500 hover:bg-blue-600 text-white shadow-sm'
                                }`}
                              >
                                <div className="flex items-center justify-center gap-2">
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                  </svg>
                                  Voltar à Conversa Atual
                                </div>
                              </button>
                            </div>
                          )}
                        </>
                      );
                    })()
                  ) : threads && threads.length > 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm">
                        Nenhuma conversa anterior
                      </p>
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm">Nenhuma conversa anterior</p>
                    </div>
                  )}
                </motion.div>
              ) : (
                // 💬 ÁREA DE CHAT
                <motion.div
                  key="chat"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="h-full"
                >
                  {messages.length === 0 ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className={`flex flex-col items-center justify-center h-full text-center ${isMobile ? 'py-4 px-3' : 'py-8 px-4'}`}
                  >
                    {/* Avatar e Título */}
                    <div className={`relative ${isMobile ? 'mb-4' : 'mb-8'}`}>
                      <div className={`${isMobile ? 'p-4' : 'p-6'} bg-gradient-to-br from-purple-500 to-indigo-600 rounded-3xl border-2 border-black dark:border-gray-600 shadow-xl`}>
                        <div className={`${isMobile ? 'text-3xl' : 'text-4xl'}`}>🧠</div>
                      </div>
                      <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
                    </div>

                    <h1 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-gray-800 dark:text-gray-200 ${isMobile ? 'mb-2' : 'mb-3'}`}>
                      Dr. Will
                    </h1>

                    <p className={`text-gray-600 dark:text-gray-400 ${isMobile ? 'mb-4 text-sm' : 'mb-8'} max-w-lg leading-relaxed`}>
                      Assistente médico especializado em discussão de casos, criação de tabelas, fluxogramas e análise de imagens
                    </p>

                    {/* Recursos */}
                    <div className={`grid grid-cols-2 md:grid-cols-4 ${isMobile ? 'gap-2 mb-4' : 'gap-4 mb-8'} w-full max-w-2xl`}>
                      <div className={`flex flex-col items-center ${isMobile ? 'p-2' : 'p-4'} bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow`}>
                        <div className={`${isMobile ? 'text-lg mb-1' : 'text-2xl mb-2'}`}>📊</div>
                        <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium text-gray-700 dark:text-gray-300 text-center`}>Tabelas</span>
                      </div>

                      <div className={`flex flex-col items-center ${isMobile ? 'p-2' : 'p-4'} bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow`}>
                        <div className={`${isMobile ? 'text-lg mb-1' : 'text-2xl mb-2'}`}>🗺️</div>
                        <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium text-gray-700 dark:text-gray-300 text-center`}>Fluxogramas</span>
                      </div>

                      <div className={`flex flex-col items-center ${isMobile ? 'p-2' : 'p-4'} bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow`}>
                        <div className={`${isMobile ? 'text-lg mb-1' : 'text-2xl mb-2'}`}>🖼️</div>
                        <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium text-gray-700 dark:text-gray-300 text-center`}>Análise de Imagens</span>
                      </div>

                      <div className={`flex flex-col items-center ${isMobile ? 'p-2' : 'p-4'} bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow`}>
                        <div className={`${isMobile ? 'text-lg mb-1' : 'text-2xl mb-2'}`}>💬</div>
                        <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium text-gray-700 dark:text-gray-300 text-center`}>Discussão de Casos</span>
                      </div>
                    </div>

                    {/* Call to Action */}
                    <div className={`bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl ${isMobile ? 'p-3' : 'p-6'} border border-blue-200 dark:border-blue-700 max-w-md`}>
                      <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700 dark:text-gray-300 ${isMobile ? 'mb-1' : 'mb-2'} font-medium`}>
                        Pronto para começar?
                      </p>
                      <p className={`${isMobile ? 'text-[10px]' : 'text-xs'} text-gray-500 dark:text-gray-400`}>
                        Digite sua pergunta ou envie uma imagem para análise
                      </p>
                    </div>
                  </motion.div>
                ) : (
                  <>
                    {/* Mensagens */}
                    {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      ref={(el) => (messageRefs.current[message.id] = el)}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="flex flex-col"
                      data-message-id={message.id}
                      data-is-user={message.isUser}
                      style={{ scrollMarginTop: '16px' }}
                    >
                  {/* Avatar acima da mensagem */}
                  <div className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} mb-2`}>
                    {message.isUser ? (
                      <Avatar className={`${isMobile ? 'h-8 w-8' : 'h-10 w-10'} border-2 border-blue-500`}>
                        <AvatarImage
                          src={user?.user_metadata?.avatar_url || `https://www.gravatar.com/avatar/${user?.email}?d=mp`}
                          alt={profile?.full_name || user?.email || "Usuário"}
                        />
                        <AvatarFallback className="bg-blue-500 text-white font-bold">
                          {profile?.full_name ? getInitials(profile.full_name) :
                           user?.email ? getInitials(user.email) : "U"}
                        </AvatarFallback>
                      </Avatar>
                    ) : (
                      <div className={`p-2 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg ${isMobile ? 'h-8 w-8' : 'h-10 w-10'} flex items-center justify-center`}>
                        <Stethoscope className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'} text-white`} />
                      </div>
                    )}
                  </div>

                  {/* Mensagem com largura total */}
                  <div className={`${message.isUser ? 'flex justify-end' : 'flex justify-start'}`}>
                    <div className={`${isMobile ? 'max-w-[95%]' : 'max-w-[90%]'}`}>
                    <div
                      className={`p-4 rounded-2xl ${
                        message.isUser
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-auto shadow-lg'
                          : 'bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-600'
                      }`}
                    >
                      {/* Conteúdo da mensagem com design especial para thinking */}
                      {message.isThinking ? (
                        // 🧠 DESIGN COMPACTO COM TOGGLE PARA THINKING MODE
                        <ThinkingModeComponent
                          content={message.content}
                          isStreaming={message.isStreaming}
                          formatThinkingContent={formatThinkingContent}
                        />
                      ) : (
                        // Conteúdo normal da mensagem
                        <>
                          {/* Mostrar imagens se for mensagem do usuário */}
                          {message.isUser && message.images && message.images.length > 0 && (
                            <MessageImageViewer
                              images={message.images}
                              className="mb-3"
                            />
                          )}

                          <IsolatedMessageContent
                            content={message.content}
                            isStreaming={message.isStreaming}
                            messageId={message.id}
                            isUser={message.isUser}
                            isLoading={isLoading}
                            onResourcesClick={handleResourcesClick}
                            onResourcesDataDetected={handleResourcesDataDetected}
                          />
                          {message.isStreaming && message.content && !message.isThinking && (
                            <div className="flex items-center gap-2 mt-3 pt-2 border-t border-gray-200">
                              <div className="flex gap-1">
                                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                              <span className="text-xs text-gray-500">Dr. Will está respondendo...</span>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 mt-1 px-2">
                      {message.timestamp.toLocaleTimeString('pt-BR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                    </div>
                  </div>
                  </motion.div>
                    ))}
                  </>
                )}
              </motion.div>
            )}
            </AnimatePresence>
            <div ref={messagesEndRef} />
          </div>



          {/* Input Area */}
          <div className={`flex-shrink-0 border-t-2 border-black dark:border-gray-600 ${isMobile ? 'p-3' : 'p-4'} bg-gray-50 dark:bg-gray-700`}>
            {/* Aviso médico */}
            <div className={`${isMobile ? 'mt-2 mb-3' : 'mb-2'} flex items-center gap-2 justify-center text-[10px] text-gray-500`}>
              <TriangleAlert className="w-4 h-4" />
              <p>Como médico é seu papel discernir todas as informações.</p>
            </div>

            {/* Preview das imagens selecionadas */}
            {selectedImages.length > 0 && (
              <div className="mb-3 flex gap-2 flex-wrap">
                {selectedImages.map((image, index) => (
                  <div key={index} className="relative">
                    <img
                      src={image.url}
                      alt={`Preview ${index + 1}`}
                      className="w-16 h-16 object-cover rounded-lg border-2 border-gray-300"
                    />
                    <button
                      onClick={() => removeImage(index)}
                      className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}

            <div className={`flex ${isMobile ? 'gap-2' : 'gap-3'}`}>
              <div className="flex-1 relative">
                <Textarea
                  ref={textareaRef}
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Digite sua pergunta..."
                  className={`w-full ${isMobile ? 'min-h-[50px] max-h-[100px]' : 'min-h-[60px] max-h-[120px]'} resize-none border-2 border-gray-300 rounded-xl focus:border-purple-500 focus:ring-0 ${isMobile ? 'pr-12' : 'pr-16'}`}
                  disabled={isLoading || isUploading}
                />

                {/* Botão de imagem mobile (dentro do textarea) */}
                {isMobile && (
                  <>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      ref={fileInputRef}
                      className="hidden"
                      multiple
                    />
                    <Button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isLoading || isUploading || selectedImages.length >= 3}
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 bottom-2 w-8 h-8 p-0 text-gray-500"
                    >
                      {isUploading ?
                        <Loader2 className="w-4 h-4 animate-spin text-blue-500" /> :
                        <ImageIcon className="w-4 h-4" />
                      }
                    </Button>
                  </>
                )}
              </div>

              {/* Botão de imagem desktop */}
              {!isMobile && (
                <>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    ref={fileInputRef}
                    className="hidden"
                    multiple
                  />
                  <Button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isLoading || isUploading || selectedImages.length >= 3}
                    variant="outline"
                    className="bg-gray-50 dark:bg-gray-600 border-2 border-gray-300 dark:border-gray-500 rounded-xl min-w-12 h-12 p-2 shadow-sm hover:bg-gray-100 dark:hover:bg-gray-500 flex items-center justify-center"
                  >
                    {isUploading ?
                      <Loader2 className="w-5 h-5 animate-spin text-blue-500" /> :
                      <ImageIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    }
                  </Button>
                </>
              )}

              {isLoading ? (
                <Button
                  onClick={cancelRequest}
                  className={`bg-red-500 hover:bg-red-600 text-white border-2 border-black dark:border-gray-600 rounded-xl ${isMobile ? 'px-3 py-2 min-w-12 h-12' : 'px-6 py-3 min-w-16 h-12'} font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center`}
                >
                  <X className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                </Button>
              ) : (
                <Button
                  onClick={() => handleSendMessage()}
                  disabled={!inputMessage.trim() && selectedImages.length === 0}
                  className={`bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white border-2 border-black dark:border-gray-600 rounded-xl ${isMobile ? 'px-3 py-2 min-w-12 h-12' : 'px-6 py-3 min-w-16 h-12'} font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center`}
                >
                  <Send className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                </Button>
              )}
            </div>

            {isLoading && !isStreaming && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="mt-4"
              >
                <div className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/30 dark:to-indigo-900/30 rounded-2xl border-2 border-purple-200 dark:border-purple-700 p-6 shadow-lg">
                  <div className="flex items-center gap-4">
                    {/* Animated Brain Icon */}
                    <div className="relative">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center">
                        <Brain className="h-6 w-6 text-white animate-pulse" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-400 rounded-full animate-ping"></div>
                      <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-indigo-400 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-lg font-semibold text-purple-800 dark:text-purple-300">Dr. Will está analisando</h4>
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                        </div>
                      </div>
                      <p className="text-purple-600 text-sm">
                        Processando sua pergunta médica com raciocínio clínico estruturado
                      </p>

                      {/* Progress bar animation */}
                      <div className="mt-3 w-full bg-purple-200 dark:bg-purple-800 rounded-full h-1.5">
                        <div className="bg-gradient-to-r from-purple-500 to-indigo-500 h-1.5 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}



            {error && (
              <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg">
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  size="sm"
                  className="mt-2 text-red-600 dark:text-red-400 border-red-300 dark:border-red-700 hover:bg-red-50 dark:hover:bg-red-900/30"
                >
                  Tentar Novamente
                </Button>
              </div>
            )}
          </div>
        </motion.div>






      </div>

      {/* Table Dialog */}
      {tableDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-7xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden border border-gray-200 dark:border-gray-600">
            {/* Dialog Header */}
            <div className="bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-600 text-white p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                    <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-lg sm:text-xl font-bold">Tabela Diagnóstica</h2>
                    <p className="text-white text-opacity-90 text-xs sm:text-sm">Dr. Will • MedEvo • Diagnósticos Diferenciais</p>
                  </div>
                </div>
                <button
                  onClick={() => setTableDialogOpen(false)}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-xl transition-all duration-200 hover:scale-105"
                >
                  <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>

            {/* Dialog Content */}
            <div className="overflow-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)]">
              {currentTableData.length > 0 && (
                <>
                  {/* Mobile View - Card Layout */}
                  <div className="block sm:hidden p-4 space-y-4">
                    {currentTableData.slice(1).map((row, rowIndex) => (
                      <div
                        key={rowIndex}
                        className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl p-4 shadow-sm"
                      >
                        <div className="flex items-center gap-2 mb-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <span className="text-white text-sm font-bold">{rowIndex + 1}</span>
                          </div>
                          <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                            <div dangerouslySetInnerHTML={{ __html: row[0] || 'Diagnóstico' }} />
                          </div>
                        </div>

                        {row.slice(1).map((cell, cellIndex) => (
                          <div key={cellIndex} className="mb-3 last:mb-0">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              <div dangerouslySetInnerHTML={{ __html: currentTableData[0]?.[cellIndex + 1] || `Campo ${cellIndex + 1}` }} />
                            </div>
                            <div className="text-sm text-gray-700 leading-relaxed">
                              <div dangerouslySetInnerHTML={{ __html: cell }} />
                            </div>
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>

                  {/* Desktop View - Table Layout */}
                  <div className="hidden sm:block p-6">
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm border border-gray-200 dark:border-gray-600">
                        <thead className="bg-gradient-to-r from-gray-50 via-blue-50 to-purple-50 dark:from-gray-700 dark:via-blue-900/30 dark:to-purple-900/30">
                          <tr>
                            {currentTableData[0]?.map((header, index) => (
                              <th
                                key={index}
                                className="px-6 py-4 text-left text-sm font-bold text-gray-900 dark:text-gray-100 border-b-2 border-gray-300 dark:border-gray-600"
                              >
                                <div dangerouslySetInnerHTML={{ __html: header }} />
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {currentTableData.slice(1).map((row, rowIndex) => (
                            <tr
                              key={rowIndex}
                              className={`${rowIndex % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-700'} hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-200`}
                            >
                              {row.map((cell, cellIndex) => (
                                <td
                                  key={cellIndex}
                                  className="px-6 py-4 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-600 align-top"
                                >
                                  <div dangerouslySetInnerHTML={{ __html: cell }} />
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Dialog Footer */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 px-4 sm:px-6 py-4 border-t border-gray-200 dark:border-gray-600">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span className="hidden sm:inline">
                    {currentTableData.length - 1} diagnósticos diferenciais • Dr. Will MedEvo
                  </span>
                  <span className="sm:hidden">
                    {currentTableData.length - 1} diagnósticos
                  </span>
                </div>
                <button
                  onClick={() => setTableDialogOpen(false)}
                  className="w-full sm:w-auto bg-gradient-to-r from-emerald-500 to-blue-600 hover:from-emerald-600 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 hover:scale-105 shadow-lg"
                >
                  <span className="flex items-center justify-center gap-2">
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Fechar
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Diagnostics Modal */}
      <DrWillDiagnostics
        isOpen={showDiagnostics}
        onClose={() => setShowDiagnostics(false)}
      />

      {/* Mermaid Modal */}
      <MermaidModal
        isOpen={mermaidModalOpen}
        onClose={() => setMermaidModalOpen(false)}
        mermaidCode={currentMermaidCode}
      />

      {/* Dialog de Sessão Inativa - Copiado do FloatingChatButton */}
      <SessionInactiveDialog
        open={sessionDialogOpen}
        onOpenChange={setSessionDialogOpen}
        sessionTitle={selectedSessionTitle}
        sessionId={selectedSessionId}
      />

      {/* Dialog de recursos de medicamentos (do código antigo) */}
      {medicationDialogData && (
        <MedicationResourcesDialog
          open={medicationDialogOpen}
          onOpenChange={setMedicationDialogOpen}
          directMentions={medicationDialogData.directMentions}
          suggestions={medicationDialogData.suggestions}
          conductMentions={medicationDialogData.conductMentions}
          conductSuggestions={medicationDialogData.conductSuggestions}
          title={medicationDialogData.title}
        />
      )}

      {/* Dialog de Confirmação de Deleção */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Confirmar Deleção
            </DialogTitle>
            <DialogDescription>
              {deleteDialogData?.type === 'all' ? (
                <>
                  Tem certeza que deseja deletar <strong>TODAS as conversas</strong>?
                  <br />
                  <span className="text-red-600 font-medium">Esta ação não pode ser desfeita.</span>
                </>
              ) : (
                <>
                  Tem certeza que deseja deletar a conversa:
                  <br />
                  <strong>"{deleteDialogData?.threadTitle}"</strong>?
                  <br />
                  <span className="text-red-600 font-medium">Esta ação não pode ser desfeita.</span>
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-3 mt-6">
            <Button
              variant="outline"
              onClick={cancelDelete}
              disabled={isDeleting}
              className="border-gray-300 hover:bg-gray-50"
            >
              Cancelar
            </Button>
            <Button
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-red-500 hover:bg-red-600 text-white disabled:opacity-50"
            >
              {isDeleting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Deletando...
                </div>
              ) : (
                deleteDialogData?.type === 'all' ? 'Deletar Todas' : 'Deletar'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>


    </div>
  );
}; // Fechamento da função DrWill

export default DrWill;
